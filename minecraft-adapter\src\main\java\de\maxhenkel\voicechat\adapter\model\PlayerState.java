package de.maxhenkel.voicechat.adapter.model;

import de.maxhenkel.voicechat.adapter.util.FriendlyByteBuf;
import java.io.IOException;
import java.util.UUID;

/**
 * 玩家语音聊天状态
 * 与原版插件兼容的PlayerState实现
 */
public class PlayerState {

    private UUID uuid;
    private String name;
    private boolean disabled;
    private boolean disconnected;
    private UUID group;

    public PlayerState(UUID uuid, String name, boolean disabled, boolean disconnected) {
        this.uuid = uuid;
        this.name = name;
        this.disabled = disabled;
        this.disconnected = disconnected;
    }

    // 兼容旧构造函数
    public PlayerState(UUID uuid, String name, boolean connected, boolean disconnected, UUID group) {
        this.uuid = uuid;
        this.name = name;
        this.disabled = false; // connected参数被忽略，使用disabled替代
        this.disconnected = disconnected;
        this.group = group;
    }
    
    // Getters
    public UUID getUuid() { return uuid; }
    public String getName() { return name; }
    public boolean isDisabled() { return disabled; }
    public boolean isDisconnected() { return disconnected; }
    public UUID getGroup() { return group; }
    public boolean hasGroup() { return group != null; }

    // Setters
    public void setUuid(UUID uuid) { this.uuid = uuid; }
    public void setName(String name) { this.name = name; }
    public void setDisabled(boolean disabled) { this.disabled = disabled; }
    public void setDisconnected(boolean disconnected) { this.disconnected = disconnected; }
    public void setGroup(UUID group) { this.group = group; }

    /**
     * 从字节缓冲区读取PlayerState（与原版插件兼容）
     */
    public static PlayerState fromBytes(FriendlyByteBuf buf) throws IOException {
        boolean disabled = buf.readBoolean();
        boolean disconnected = buf.readBoolean();
        UUID uuid = buf.readUUID();
        String name = buf.readUtf(); // 使用默认的readUtf方法

        PlayerState state = new PlayerState(uuid, name, disabled, disconnected);

        if (buf.readBoolean()) {
            state.setGroup(buf.readUUID());
        }

        return state;
    }

    /**
     * 将PlayerState写入字节缓冲区（与原版插件兼容）
     */
    public void toBytes(FriendlyByteBuf buf) throws IOException {
        buf.writeBoolean(disabled);
        buf.writeBoolean(disconnected);
        buf.writeUUID(uuid);
        buf.writeUtf(name); // 使用默认的writeUtf方法
        buf.writeBoolean(hasGroup());
        if (hasGroup()) {
            buf.writeUUID(group);
        }
    }

    @Override
    public String toString() {
        return String.format("PlayerState{uuid=%s, name='%s', disabled=%s, disconnected=%s, group=%s}",
                           uuid, name, disabled, disconnected, group);
    }
}
