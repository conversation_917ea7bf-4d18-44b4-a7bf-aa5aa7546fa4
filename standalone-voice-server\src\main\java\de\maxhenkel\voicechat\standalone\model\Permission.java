package de.maxhenkel.voicechat.standalone.model;

/**
 * 语音聊天权限枚举
 */
public enum Permission {
    /**
     * 基本语音聊天权限
     */
    SPEAK("voicechat.speak"),
    
    /**
     * 群组聊天权限
     */
    GROUPS("voicechat.groups"),
    
    /**
     * 管理员权限
     */
    ADMIN("voicechat.admin"),
    
    /**
     * 录音权限
     */
    RECORD("voicechat.record"),
    
    /**
     * 旁观者交互权限
     */
    SPECTATOR_INTERACTION("voicechat.spectator.interaction"),
    
    /**
     * 旁观者附身权限
     */
    SPECTATOR_POSSESSION("voicechat.spectator.possession");
    
    private final String permissionNode;
    
    Permission(String permissionNode) {
        this.permissionNode = permissionNode;
    }
    
    public String getPermissionNode() {
        return permissionNode;
    }
    
    /**
     * 从权限节点字符串获取权限枚举
     */
    public static Permission fromNode(String node) {
        for (Permission permission : values()) {
            if (permission.permissionNode.equals(node)) {
                return permission;
            }
        }
        return null;
    }
    
    @Override
    public String toString() {
        return permissionNode;
    }
}
