package de.maxhenkel.voicechat.standalone.api;

import de.maxhenkel.voicechat.standalone.config.ServerConfig;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 配置API处理器
 */
public class ConfigApiHandler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigApiHandler.class);
    
    private final ServerConfig config;
    
    public ConfigApiHandler(ServerConfig config) {
        this.config = config;
    }
    
    /**
     * 获取配置
     * GET /api/config
     */
    public void handleGetConfig(Context ctx) {
        try {
            Map<String, Object> configMap = new HashMap<>();
            
            // 服务器配置
            Map<String, Object> serverConfig = new HashMap<>();
            serverConfig.put("host", config.getServer().getHost());
            serverConfig.put("port", config.getServer().getPort());
            serverConfig.put("bindAddress", config.getServer().getBindAddress());
            configMap.put("server", serverConfig);
            
            // API配置（不包含敏感信息）
            Map<String, Object> apiConfig = new HashMap<>();
            apiConfig.put("host", config.getApi().getHost());
            apiConfig.put("port", config.getApi().getPort());
            configMap.put("api", apiConfig);
            
            // 语音配置
            Map<String, Object> voiceConfig = new HashMap<>();
            voiceConfig.put("codec", config.getVoice().getCodec());
            voiceConfig.put("mtuSize", config.getVoice().getMtuSize());
            voiceConfig.put("keepAlive", config.getVoice().getKeepAlive());
            voiceConfig.put("maxDistance", config.getVoice().getMaxDistance());
            voiceConfig.put("groupsEnabled", config.getVoice().isGroupsEnabled());
            voiceConfig.put("allowRecording", config.getVoice().isAllowRecording());
            configMap.put("voice", voiceConfig);
            
            // 安全配置
            Map<String, Object> securityConfig = new HashMap<>();
            securityConfig.put("encryptionEnabled", config.getSecurity().isEncryptionEnabled());
            securityConfig.put("authTimeout", config.getSecurity().getAuthTimeout());
            configMap.put("security", securityConfig);
            
            // Minecraft服务器配置
            configMap.put("minecraft", config.getMinecraft().getServers());
            
            ctx.json(configMap);
            
        } catch (Exception e) {
            LOGGER.error("Error getting config", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 更新配置
     * PUT /api/config
     */
    public void handleUpdateConfig(Context ctx) {
        try {
            // 注意：这里只是示例，实际实现中需要更复杂的配置更新逻辑
            // 因为ServerConfig的字段大多是final的，需要重新设计配置更新机制
            
            ctx.status(HttpStatus.NOT_IMPLEMENTED)
               .json(Map.of(
                   "error", "Configuration update not implemented",
                   "message", "Configuration updates require server restart"
               ));
            
        } catch (Exception e) {
            LOGGER.error("Error updating config", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
}
