package de.maxhenkel.voicechat.standalone.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 独立语音服务器配置类
 */
public class ServerConfig {
    
    @JsonProperty("server")
    private ServerSettings server = new ServerSettings();
    
    @JsonProperty("api")
    private ApiSettings api = new ApiSettings();
    
    @JsonProperty("minecraft")
    private MinecraftSettings minecraft = new MinecraftSettings();
    
    @JsonProperty("voice")
    private VoiceSettings voice = new VoiceSettings();
    
    @JsonProperty("security")
    private SecuritySettings security = new SecuritySettings();
    
    // Getters
    public ServerSettings getServer() { return server; }
    public ApiSettings getApi() { return api; }
    public MinecraftSettings getMinecraft() { return minecraft; }
    public VoiceSettings getVoice() { return voice; }
    public SecuritySettings getSecurity() { return security; }
    
    public static class ServerSettings {
        @JsonProperty("host")
        private String host = "0.0.0.0";
        
        @JsonProperty("port")
        private int port = 24454;
        
        @JsonProperty("bind_address")
        private String bindAddress = "";
        
        // Getters
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getBindAddress() { return bindAddress; }
    }
    
    public static class ApiSettings {
        @JsonProperty("host")
        private String host = "0.0.0.0";
        
        @JsonProperty("port")
        private int port = 8080;
        
        @JsonProperty("auth_token")
        private String authToken = "change-this-secret-token";
        
        // Getters
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getAuthToken() { return authToken; }
    }
    
    public static class MinecraftSettings {
        @JsonProperty("servers")
        private List<MinecraftServer> servers = List.of(new MinecraftServer());
        
        // Getters
        public List<MinecraftServer> getServers() { return servers; }
        
        public static class MinecraftServer {
            @JsonProperty("name")
            private String name = "default";
            
            @JsonProperty("host")
            private String host = "localhost";
            
            @JsonProperty("api_endpoint")
            private String apiEndpoint = "http://localhost:8081/voicechat";
            
            // Getters
            public String getName() { return name; }
            public String getHost() { return host; }
            public String getApiEndpoint() { return apiEndpoint; }
        }
    }
    
    public static class VoiceSettings {
        @JsonProperty("codec")
        private String codec = "VOIP";
        
        @JsonProperty("mtu_size")
        private int mtuSize = 1024;
        
        @JsonProperty("keep_alive")
        private int keepAlive = 1000;
        
        @JsonProperty("max_distance")
        private double maxDistance = 48.0;

        @JsonProperty("whisper_distance_multiplier")
        private double whisperDistanceMultiplier = 0.5;

        @JsonProperty("groups_enabled")
        private boolean groupsEnabled = true;

        @JsonProperty("allow_recording")
        private boolean allowRecording = true;
        
        // Getters
        public String getCodec() { return codec; }
        public int getMtuSize() { return mtuSize; }
        public int getKeepAlive() { return keepAlive; }
        public double getMaxDistance() { return maxDistance; }
        public double getWhisperDistanceMultiplier() { return whisperDistanceMultiplier; }
        public double getWhisperDistance() { return maxDistance * whisperDistanceMultiplier; }
        public boolean isGroupsEnabled() { return groupsEnabled; }
        public boolean isAllowRecording() { return allowRecording; }
    }
    
    public static class SecuritySettings {
        @JsonProperty("encryption_enabled")
        private boolean encryptionEnabled = true;
        
        @JsonProperty("auth_timeout")
        private int authTimeout = 30000;
        
        // Getters
        public boolean isEncryptionEnabled() { return encryptionEnabled; }
        public int getAuthTimeout() { return authTimeout; }
    }
    
    /**
     * 从文件加载配置
     */
    public static ServerConfig load(File file) throws IOException {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        return mapper.readValue(file, ServerConfig.class);
    }
    
    /**
     * 保存配置到文件
     */
    public void save(File file) throws IOException {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        mapper.writeValue(file, this);
    }
    
    /**
     * 创建默认配置文件
     */
    public static void createDefaultConfig(File file) throws IOException {
        ServerConfig defaultConfig = new ServerConfig();
        defaultConfig.save(file);
    }
}
