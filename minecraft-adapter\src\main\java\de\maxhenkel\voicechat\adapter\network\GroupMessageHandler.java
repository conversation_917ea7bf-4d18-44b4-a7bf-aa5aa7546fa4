package de.maxhenkel.voicechat.adapter.network;

import de.maxhenkel.voicechat.adapter.VoiceChatAdapterPlugin;
import de.maxhenkel.voicechat.adapter.managers.PlayerStateManager;
import de.maxhenkel.voicechat.adapter.util.FriendlyByteBuf;
import org.bukkit.entity.Player;
import org.bukkit.plugin.messaging.PluginMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;
import java.util.UUID;

/**
 * 处理群组相关的插件消息
 */
public class GroupMessageHandler implements PluginMessageListener {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(GroupMessageHandler.class);
    
    private final VoiceChatAdapterPlugin plugin;
    private final VoiceServerClient voiceServerClient;
    private final PlayerStateManager playerStateManager;

    public GroupMessageHandler(VoiceChatAdapterPlugin plugin, VoiceServerClient voiceServerClient, PlayerStateManager playerStateManager) {
        this.plugin = plugin;
        this.voiceServerClient = voiceServerClient;
        this.playerStateManager = playerStateManager;
    }

    /**
     * 当玩家加入服务器时，同步所有现有群组
     */
    public void syncGroupsForPlayer(Player player) {
        try {
            LOGGER.info("Starting group sync for player {}", player.getName());

            // 测试与独立语音服务器的连接
            try {
                Map<String, Object> serverStatus = voiceServerClient.getServerStatus();
                LOGGER.debug("Voice server status: {}", serverStatus);
            } catch (Exception e) {
                LOGGER.error("Failed to connect to voice server for status check: {}", e.getMessage());
                return;
            }

            // 从独立语音服务器获取所有群组
            java.util.List<Map<String, Object>> allGroups = voiceServerClient.getAllGroups();

            if (allGroups != null && !allGroups.isEmpty()) {
                LOGGER.info("Syncing {} groups for player {}", allGroups.size(), player.getName());

                for (Map<String, Object> groupInfo : allGroups) {
                    try {
                        LOGGER.debug("Processing group info: {}", groupInfo);

                        UUID groupUuid = UUID.fromString((String) groupInfo.get("id"));
                        String groupName = (String) groupInfo.get("name");
                        boolean hasPassword = (Boolean) groupInfo.get("hasPassword");
                        int groupType = ((Number) groupInfo.get("type")).intValue();

                        sendAddGroupPacketFromCreateResponse(player, groupUuid, groupName, groupType, hasPassword);
                        LOGGER.info("Successfully synced group '{}' (UUID: {}) for player {}", groupName, groupUuid, player.getName());
                    } catch (Exception e) {
                        LOGGER.error("Failed to sync individual group for player {}: {}", player.getName(), e.getMessage(), e);
                    }
                }
            } else {
                LOGGER.warn("No groups returned from voice server for player {} (allGroups={})", player.getName(), allGroups);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to sync groups for player {}: {}", player.getName(), e.getMessage(), e);
        }
    }
    
    @Override
    public void onPluginMessageReceived(String channel, Player player, byte[] message) {
        LOGGER.debug("Received plugin message: channel={}, player={}, messageLength={}",
                    channel, player.getName(), message.length);

        if (!channel.startsWith("voicechat:")) {
            LOGGER.debug("Ignoring non-voicechat channel: {}", channel);
            return;
        }

        try {
            String subChannel = channel.substring("voicechat:".length());
            LOGGER.info("Processing voice chat message: {} from {}", subChannel, player.getName());
            handleVoiceChatMessage(subChannel, player, message);
        } catch (Exception e) {
            LOGGER.error("Failed to handle voice chat message from {}: {}", player.getName(), e.getMessage(), e);
        }
    }
    
    private void handleVoiceChatMessage(String subChannel, Player player, byte[] message) throws IOException {
        LOGGER.debug("Received voice chat message: {} from {}", subChannel, player.getName());
        
        switch (subChannel) {
            case "create_group":
                handleCreateGroup(player, message);
                break;
            case "set_group":
                handleJoinGroup(player, message);
                break;
            case "leave_group":
                handleLeaveGroup(player, message);
                break;
            default:
                LOGGER.debug("Unknown voice chat message type: {}", subChannel);
                break;
        }
    }
    
    private void handleCreateGroup(Player player, byte[] message) throws IOException {
        LOGGER.debug("Handling create group message, data length: {}", message.length);

        if (message.length == 0) {
            LOGGER.warn("Received empty create group message from {}", player.getName());
            return;
        }

        try (FriendlyByteBuf buf = new FriendlyByteBuf(message)) {
            // 按照Paper版本的格式读取数据
            // 1. 读取群组名称（VarInt长度前缀的UTF-8字符串）
            String groupName = buf.readUtf(512);
            LOGGER.debug("Read group name: {}", groupName);

            // 2. 读取密码（可选）
            String password = null;
            if (buf.readBoolean()) {
                password = buf.readUtf(512);
                LOGGER.debug("Read group password (length: {})", password != null ? password.length() : 0);
            }

            // 3. 读取群组类型
            int groupType = buf.readShort();
            LOGGER.debug("Read group type: {}", groupType);
        
            LOGGER.info("Player {} wants to create group: {} (type: {})", player.getName(), groupName, groupType);

            try {
                // 转发到独立语音服务器
                UUID groupUuid = voiceServerClient.createGroup(player.getUniqueId(), groupName, password, groupType);

                if (groupUuid != null) {
                    LOGGER.info("Successfully created group {} with UUID {} for player {}", groupName, groupUuid, player.getName());

                    // 向所有在线玩家广播群组信息（与原项目一致）
                    broadcastGroupToAllPlayers(groupUuid, groupName, groupType, password != null);

                    // 发送成功响应给创建者
                    sendGroupCreatedResponse(player, groupUuid, null, groupName, groupType, password);

                    // 同步创建者的群组状态
                    syncPlayerGroupState(player, groupUuid);
                } else {
                    LOGGER.warn("Failed to create group {} for player {}", groupName, player.getName());
                    sendGroupCreatedResponse(player, null, "Failed to create group", groupName, groupType, password);
                }
            } catch (Exception e) {
                LOGGER.error("Error creating group {} for player {}: {}", groupName, player.getName(), e.getMessage());
                sendGroupCreatedResponse(player, null, e.getMessage(), groupName, groupType, password);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to parse create group message from {}: {}", player.getName(), e.getMessage());
        }
    }
    
    private void handleJoinGroup(Player player, byte[] message) throws IOException {
        LOGGER.debug("Handling join group message, data length: {}", message.length);

        if (message.length == 0) {
            LOGGER.warn("Received empty join group message from {}", player.getName());
            return;
        }

        try (FriendlyByteBuf buf = new FriendlyByteBuf(message)) {
            // 按照Paper版本的格式读取数据
            // 1. 读取群组UUID
            UUID groupUuid = buf.readUUID();
            LOGGER.debug("Read group UUID: {}", groupUuid);

            // 2. 读取密码（可选）
            String password = null;
            if (buf.readBoolean()) {
                password = buf.readUtf(512);
                LOGGER.debug("Read group password");
            }
        
            LOGGER.info("Player {} wants to join group: {}", player.getName(), groupUuid);

            try {
                // 转发到独立语音服务器
                boolean success = voiceServerClient.joinGroup(player.getUniqueId(), groupUuid, password);

                if (success) {
                    LOGGER.info("Successfully joined group {} for player {}", groupUuid, player.getName());

                    // 更新玩家状态管理器中的群组信息
                    playerStateManager.setGroup(player, groupUuid);

                    // 发送加入群组响应
                    sendJoinedGroupResponse(player, groupUuid, false);
                } else {
                    LOGGER.warn("Failed to join group {} for player {}", groupUuid, player.getName());
                    sendJoinedGroupResponse(player, null, true);
                }
            } catch (Exception e) {
                LOGGER.error("Error joining group {} for player {}: {}", groupUuid, player.getName(), e.getMessage());
                sendJoinedGroupResponse(player, null, true);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to parse join group message from {}: {}", player.getName(), e.getMessage());
        }
    }
    
    private void handleLeaveGroup(Player player, byte[] message) {
        LOGGER.info("Player {} wants to leave group", player.getName());
        
        try {
            // 转发到独立语音服务器
            boolean success = voiceServerClient.leaveGroup(player.getUniqueId());
            
            if (success) {
                LOGGER.info("Successfully left group for player {}", player.getName());

                // 更新玩家状态管理器中的群组信息
                playerStateManager.setGroup(player, null);

                sendJoinedGroupResponse(player, null, false);
            } else {
                LOGGER.warn("Failed to leave group for player {}", player.getName());
            }
        } catch (Exception e) {
            LOGGER.error("Error leaving group for player {}: {}", player.getName(), e.getMessage());
        }
    }
    
    private void sendGroupCreatedResponse(Player player, UUID groupUuid, String error, String groupName, int groupType, String password) {
        if (groupUuid != null) {
            // 群组创建成功，先发送群组信息，然后发送JoinedGroupPacket
            LOGGER.info("Sending group created response to {}: groupUuid={}", player.getName(), groupUuid);

            // 先发送群组信息给客户端（使用创建时的数据）
            sendAddGroupPacketFromCreateResponse(player, groupUuid, groupName, groupType, password != null);

            // 然后发送加入群组的响应
            sendJoinedGroupResponse(player, groupUuid, false);
        } else {
            // 群组创建失败
            LOGGER.warn("Sending group creation failed response to {}: error={}", player.getName(), error);
            sendJoinedGroupResponse(player, null, false);
        }
    }

    private void sendGroupJoinedResponse(Player player, UUID groupUuid, boolean wrongPassword) {
        sendJoinedGroupResponse(player, groupUuid, wrongPassword);
    }

    /**
     * 发送AddGroupPacket（群组信息）- 使用创建响应中的数据
     */
    private void sendAddGroupPacketFromCreateResponse(Player player, UUID groupUuid, String groupName, int groupType, boolean hasPassword) {
        try (FriendlyByteBuf buf = new FriendlyByteBuf()) {
            // 按照ClientGroup.toBytes格式写入群组信息
            buf.writeUUID(groupUuid);                    // UUID id
            buf.writeUtf(groupName, 512);               // String name
            buf.writeBoolean(hasPassword);              // boolean hasPassword
            buf.writeBoolean(false);                    // boolean persistent (默认false)
            buf.writeBoolean(false);                    // boolean hidden (默认false)
            buf.writeShort((short) groupType);          // short type

            byte[] data = buf.toByteArray();
            player.sendPluginMessage(plugin, "voicechat:add_group", data);

            LOGGER.debug("Sent add_group packet to {}: group={} (type={})", player.getName(), groupName, groupType);
        } catch (Exception e) {
            LOGGER.error("Failed to send add_group packet to {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * 发送AddGroupPacket（群组信息）- 从API获取
     */
    private void sendAddGroupPacket(Player player, UUID groupUuid) {
        try {
            // 从独立语音服务器获取群组详细信息
            Map<String, Object> groupInfo = voiceServerClient.getGroupInfo(groupUuid);
            if (groupInfo == null) {
                LOGGER.warn("Failed to get group info for {}", groupUuid);
                return;
            }

            try (FriendlyByteBuf buf = new FriendlyByteBuf()) {
                // 按照ClientGroup.toBytes格式写入群组信息
                buf.writeUUID(groupUuid);                                    // UUID id
                buf.writeUtf((String) groupInfo.get("name"), 512);          // String name
                buf.writeBoolean((Boolean) groupInfo.get("hasPassword"));   // boolean hasPassword
                buf.writeBoolean(false);                                     // boolean persistent (默认false)
                buf.writeBoolean(false);                                     // boolean hidden (默认false)

                // 群组类型转换
                int typeId = ((Number) groupInfo.get("type")).intValue();
                buf.writeShort((short) typeId);                              // short type

                byte[] data = buf.toByteArray();
                player.sendPluginMessage(plugin, "voicechat:add_group", data);

                LOGGER.debug("Sent add_group packet to {}: group={}", player.getName(), groupInfo.get("name"));
            }
        } catch (Exception e) {
            LOGGER.error("Failed to send add_group packet to {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * 向所有在线玩家广播群组信息
     */
    private void broadcastGroupToAllPlayers(UUID groupUuid, String groupName, int groupType, boolean hasPassword) {
        LOGGER.info("Broadcasting group '{}' to all online players", groupName);

        // 获取所有在线玩家
        for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
            try {
                sendAddGroupPacketFromCreateResponse(onlinePlayer, groupUuid, groupName, groupType, hasPassword);
                LOGGER.debug("Broadcasted group '{}' to player {}", groupName, onlinePlayer.getName());
            } catch (Exception e) {
                LOGGER.warn("Failed to broadcast group '{}' to player {}: {}", groupName, onlinePlayer.getName(), e.getMessage());
            }
        }
    }

    /**
     * 发送JoinedGroupPacket响应
     */
    private void sendJoinedGroupResponse(Player player, UUID groupUuid, boolean wrongPassword) {
        try (FriendlyByteBuf buf = new FriendlyByteBuf()) {
            // 按照Paper版本的JoinedGroupPacket格式
            buf.writeBoolean(groupUuid != null);
            if (groupUuid != null) {
                buf.writeUUID(groupUuid);
            }
            buf.writeBoolean(wrongPassword);

            byte[] data = buf.toByteArray();
            player.sendPluginMessage(plugin, "voicechat:joined_group", data);

            LOGGER.debug("Sent joined_group response to {}: group={}, wrongPassword={}",
                        player.getName(), groupUuid, wrongPassword);
        } catch (IOException e) {
            LOGGER.error("Failed to send joined_group response to {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * 发送群组成员更新
     */
    private void sendGroupMemberUpdate(Player player, UUID groupUuid) {
        try {
            // 获取群组详细信息（包括成员列表）
            Map<String, Object> groupInfo = voiceServerClient.getGroupInfo(groupUuid);
            if (groupInfo == null) {
                LOGGER.warn("Failed to get group info for member update: {}", groupUuid);
                return;
            }

            // 获取群组成员列表
            java.util.List<Map<String, Object>> members =
                (java.util.List<Map<String, Object>>) groupInfo.get("members");

            if (members != null && !members.isEmpty()) {
                LOGGER.info("Sending group member update to {}: {} members in group {}",
                           player.getName(), members.size(), groupInfo.get("name"));

                // 向群组中的所有在线成员发送成员列表更新
                for (Map<String, Object> memberInfo : members) {
                    String memberUuidStr = (String) memberInfo.get("uuid");
                    Boolean isOnline = (Boolean) memberInfo.get("online");

                    if (isOnline != null && isOnline) {
                        UUID memberUuid = UUID.fromString(memberUuidStr);
                        Player member = plugin.getServer().getPlayer(memberUuid);

                        if (member != null && member.isOnline()) {
                            // 这里可以发送一个自定义的成员状态更新包
                            // 暂时通过日志显示群组成员信息
                            LOGGER.debug("Group member: {} ({})", memberInfo.get("name"), memberUuid);
                        }
                    }
                }
            } else {
                LOGGER.debug("No members found in group {}", groupUuid);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to send group member update to {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * 同步玩家的群组状态
     */
    private void syncPlayerGroupState(Player player, UUID groupUuid) {
        try {
            // 更新 PlayerStateManager 中的群组状态
            PlayerStateManager stateManager = plugin.getPlayerStateManager();
            if (stateManager != null) {
                stateManager.setGroup(player, groupUuid);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to sync player group state", e);
        }
    }

}
