# 独立语音服务器配置文件

# 语音服务器配置
server:
  # 绑定地址，0.0.0.0 表示绑定所有网络接口
  host: "0.0.0.0"
  # UDP端口，用于语音数据传输
  port: 24454
  # 特定绑定地址，留空使用上面的host
  bind_address: ""

# API服务器配置
api:
  # API服务器地址
  host: "0.0.0.0"
  # API服务器端口
  port: 8080
  # 认证令牌，Minecraft服务器需要使用此令牌访问API
  auth_token: "change-this-secret-token"

# Minecraft服务器配置
minecraft:
  servers:
    - name: "survival"
      host: "localhost"
      api_endpoint: "http://localhost:8081/voicechat"
    - name: "creative"
      host: "localhost"
      api_endpoint: "http://localhost:8082/voicechat"

# 语音配置
voice:
  # 音频编解码器：VOIP, AUDIO, RESTRICTED_LOWDELAY
  codec: "VOIP"
  # 最大传输单元大小（字节）
  mtu_size: 1024
  # 心跳间隔（毫秒）
  keep_alive: 1000
  # 最大语音距离
  max_distance: 48.0
  # 耳语距离倍数（相对于最大距离）
  whisper_distance_multiplier: 0.5
  # 是否启用群组聊天
  groups_enabled: true
  # 是否允许录音
  allow_recording: true

# 安全配置
security:
  # 是否启用加密
  encryption_enabled: true
  # 认证超时时间（毫秒）
  auth_timeout: 30000
