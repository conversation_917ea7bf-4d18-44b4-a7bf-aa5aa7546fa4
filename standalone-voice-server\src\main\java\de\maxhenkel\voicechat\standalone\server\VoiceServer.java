package de.maxhenkel.voicechat.standalone.server;

import de.maxhenkel.voicechat.standalone.config.ServerConfig;
import de.maxhenkel.voicechat.standalone.model.PlayerData;
import de.maxhenkel.voicechat.standalone.network.NetworkMessage;
import de.maxhenkel.voicechat.standalone.network.Packet;
import de.maxhenkel.voicechat.standalone.network.packets.*;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 独立语音服务器核心类
 */
public class VoiceServer {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceServer.class);
    
    private final ServerConfig config;
    private final PlayerManager playerManager;
    private final GroupManager groupManager;
    private final ConnectionManager connectionManager;
    
    private EventLoopGroup group;
    private Channel channel;
    private ScheduledExecutorService scheduler;
    
    // 客户端连接映射
    private final ConcurrentHashMap<UUID, ClientConnection> connections = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<SocketAddress, UUID> addressToPlayer = new ConcurrentHashMap<>();
    
    public VoiceServer(ServerConfig config) {
        this.config = config;
        this.playerManager = new PlayerManager();
        this.groupManager = new GroupManager();
        this.connectionManager = new ConnectionManager(this);
    }
    
    /**
     * 启动语音服务器
     */
    public void start() throws Exception {
        LOGGER.info("Starting voice server...");
        
        group = new NioEventLoopGroup();
        scheduler = Executors.newScheduledThreadPool(2);
        
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(group)
                .channel(NioDatagramChannel.class)
                .option(ChannelOption.SO_BROADCAST, true)
                .handler(new ChannelInitializer<NioDatagramChannel>() {
                    @Override
                    protected void initChannel(NioDatagramChannel ch) {
                        ch.pipeline().addLast(new VoiceServerHandler());
                    }
                });
        
        String bindAddress = config.getServer().getBindAddress();
        if (bindAddress.isEmpty()) {
            bindAddress = config.getServer().getHost();
        }
        
        channel = bootstrap.bind(bindAddress, config.getServer().getPort()).sync().channel();
        
        // 启动定时任务
        startScheduledTasks();
        
        LOGGER.info("Voice server started on {}:{}", bindAddress, config.getServer().getPort());
    }
    
    /**
     * 停止语音服务器
     */
    public void stop() throws Exception {
        LOGGER.info("Stopping voice server...");
        
        if (scheduler != null) {
            scheduler.shutdown();
        }
        
        if (channel != null) {
            channel.close().sync();
        }
        
        if (group != null) {
            group.shutdownGracefully().sync();
        }
        
        LOGGER.info("Voice server stopped");
    }
    
    /**
     * 启动定时任务
     */
    private void startScheduledTasks() {
        // 心跳检查任务
        scheduler.scheduleAtFixedRate(this::checkKeepAlive, 
                config.getVoice().getKeepAlive(), 
                config.getVoice().getKeepAlive(), 
                TimeUnit.MILLISECONDS);
        
        // 连接超时检查任务
        scheduler.scheduleAtFixedRate(this::checkTimeouts, 5000, 5000, TimeUnit.MILLISECONDS);

        // 密钥清理任务（每5分钟执行一次）
        scheduler.scheduleAtFixedRate(this::cleanupSecrets, 300000, 300000, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 发送心跳包
     */
    private void checkKeepAlive() {
        // KeepAlivePacket不包含时间戳数据（与原项目一致）
        KeepAlivePacket keepAlive = new KeepAlivePacket();

        connections.values().forEach(connection -> {
            try {
                sendPacket(keepAlive, connection.getAddress());
            } catch (Exception e) {
                LOGGER.warn("Failed to send keep alive to {}", connection.getAddress(), e);
            }
        });
    }
    
    /**
     * 检查连接超时
     */
    private void checkTimeouts() {
        long currentTime = System.currentTimeMillis();
        long timeout = config.getVoice().getKeepAlive() * 10; // 10倍心跳间隔作为超时时间
        
        connections.entrySet().removeIf(entry -> {
            ClientConnection connection = entry.getValue();
            if (currentTime - connection.getLastKeepAlive() > timeout) {
                LOGGER.info("Client {} timed out", entry.getKey());
                addressToPlayer.remove(connection.getAddress());
                playerManager.setPlayerVoiceConnected(entry.getKey(), false);
                return true;
            }
            return false;
        });
    }

    /**
     * 清理离线玩家的密钥
     */
    private void cleanupSecrets() {
        try {
            connectionManager.cleanupOfflinePlayerSecrets();
            LOGGER.debug("Cleaned up offline player secrets");
        } catch (Exception e) {
            LOGGER.warn("Failed to cleanup secrets", e);
        }
    }

    /**
     * 发送数据包到指定地址
     */
    public void sendPacket(Packet<?> packet, SocketAddress address) throws Exception {
        // 获取目标玩家UUID
        UUID targetPlayerUuid = addressToPlayer.get(address);
        if (targetPlayerUuid == null) {
            LOGGER.warn("Cannot send packet to unknown address: {}", address);
            return;
        }

        // 获取目标玩家的密钥
        UUID secret = playerManager.getPlayerSecret(targetPlayerUuid);
        if (secret == null) {
            LOGGER.warn("Cannot send packet to player {} - no secret", targetPlayerUuid);
            return;
        }

        // 创建网络消息并加密
        NetworkMessage message = new NetworkMessage(packet, address);
        byte[] data = message.toBytes(targetPlayerUuid, secret);

        DatagramPacket datagramPacket = new DatagramPacket(
                Unpooled.wrappedBuffer(data),
                (InetSocketAddress) address
        );

        channel.writeAndFlush(datagramPacket);
    }
    
    /**
     * 处理接收到的网络消息
     */
    private void handleMessage(NetworkMessage message) {
        Packet<?> packet = message.getPacket();
        SocketAddress address = message.getAddress();

        if (packet instanceof AuthenticatePacket) {
            handleAuthenticate((AuthenticatePacket) packet, address);
        } else if (packet instanceof MicPacket) {
            handleMicPacket((MicPacket) packet, address);
        } else if (packet instanceof KeepAlivePacket) {
            handleKeepAlive((KeepAlivePacket) packet, address);
        } else if (packet instanceof PingPacket) {
            handlePing((PingPacket) packet, address);
        } else if (packet instanceof ConnectionCheckPacket) {
            handleConnectionCheck((ConnectionCheckPacket) packet, address);
        } else if (packet instanceof PlayerSoundPacket) {
            handlePlayerSound((PlayerSoundPacket) packet, address);
        } else if (packet instanceof GroupSoundPacket) {
            handleGroupSound((GroupSoundPacket) packet, address);
        } else if (packet instanceof LocationSoundPacket) {
            handleLocationSound((LocationSoundPacket) packet, address);
        }
    }
    
    /**
     * 处理客户端认证
     */
    private void handleAuthenticate(AuthenticatePacket packet, SocketAddress address) {
        UUID playerUuid = packet.getPlayerUuid();
        UUID secret = packet.getSecret();

        // 验证玩家和密钥
        PlayerData player = playerManager.getPlayer(playerUuid);
        if (player == null || !player.isOnline()) {
            LOGGER.warn("Authentication failed for unknown player: {}", playerUuid);
            return;
        }

        // 验证密钥
        UUID expectedSecret = playerManager.getPlayerSecret(playerUuid);
        if (expectedSecret == null || !expectedSecret.equals(secret)) {
            LOGGER.warn("Authentication failed for player {} - invalid secret", player.getName());
            return;
        }

        // 创建连接
        ClientConnection connection = new ClientConnection(playerUuid, address);
        connections.put(playerUuid, connection);
        addressToPlayer.put(address, playerUuid);

        // 更新玩家状态
        playerManager.setPlayerVoiceConnected(playerUuid, true);

        LOGGER.info("Player {} authenticated from {}", player.getName(), address);

        // 发送认证确认包
        try {
            AuthenticateAckPacket ackPacket = new AuthenticateAckPacket();
            sendPacket(ackPacket, address);
        } catch (Exception e) {
            LOGGER.warn("Failed to send authentication ack to {}", address, e);
        }
    }
    
    /**
     * 处理麦克风音频包
     */
    private void handleMicPacket(MicPacket packet, SocketAddress address) {
        UUID playerUuid = addressToPlayer.get(address);
        if (playerUuid == null) {
            LOGGER.warn("Received mic packet from unauthenticated address: {}", address);
            return;
        }
        
        PlayerData sender = playerManager.getPlayer(playerUuid);
        if (sender == null) {
            return;
        }
        
        // 处理语音数据（支持群组语音和群组类型）

        if (sender.getGroupId() != null) {
            processGroupVoiceData(sender, packet);
        } else {
            processVoiceData(sender, packet);
        }
    }
    
    /**
     * 处理心跳包
     */
    private void handleKeepAlive(KeepAlivePacket packet, SocketAddress address) {
        UUID playerUuid = addressToPlayer.get(address);
        if (playerUuid != null) {
            ClientConnection connection = connections.get(playerUuid);
            if (connection != null) {
                connection.updateKeepAlive();
            }
        }
    }

    /**
     * 处理Ping包
     */
    private void handlePing(PingPacket packet, SocketAddress address) {
        try {
            // 响应Ping请求（返回相同的时间戳）
            PingPacket response = new PingPacket(packet.getTimestamp());
            sendPacket(response, address);
        } catch (Exception e) {
            LOGGER.warn("Failed to send ping response to {}", address, e);
        }
    }

    /**
     * 处理连接检查包
     */
    private void handleConnectionCheck(ConnectionCheckPacket packet, SocketAddress address) {
        try {
            // 响应连接检查
            ConnectionCheckAckPacket ackPacket = new ConnectionCheckAckPacket();
            sendPacket(ackPacket, address);
        } catch (Exception e) {
            LOGGER.warn("Failed to send connection check ack to {}", address, e);
        }
    }

    /**
     * 处理玩家音频包
     */
    private void handlePlayerSound(PlayerSoundPacket packet, SocketAddress address) {
        // 玩家音频包通常是服务器发送给客户端的，客户端不应该发送这种包
    }

    /**
     * 处理群组音频包
     */
    private void handleGroupSound(GroupSoundPacket packet, SocketAddress address) {
        // 群组音频包通常是服务器发送给客户端的，客户端不应该发送这种包
        // 但如果客户端发送，我们可以将其转发给群组成员
        UUID playerUuid = addressToPlayer.get(address);
        if (playerUuid == null) {
            LOGGER.warn("Received group sound packet from unauthenticated address: {}", address);
            return;
        }

        PlayerData sender = playerManager.getPlayer(playerUuid);
        if (sender == null) {
            return;
        }

        // 广播给群组成员
        broadcastToGroupMembers(sender, packet);
    }

    /**
     * 处理位置音频包
     */
    private void handleLocationSound(LocationSoundPacket packet, SocketAddress address) {
        // 位置音频包通常是服务器发送给客户端的，客户端不应该发送这种包
        // 但如果客户端发送，我们可以将其转发给附近的玩家
        UUID playerUuid = addressToPlayer.get(address);
        if (playerUuid == null) {
            LOGGER.warn("Received location sound packet from unauthenticated address: {}", address);
            return;
        }

        PlayerData sender = playerManager.getPlayer(playerUuid);
        if (sender == null) {
            return;
        }

        // 广播给指定位置附近的玩家
        broadcastToLocationNearbyPlayers(sender, packet);
    }
    
    /**
     * 处理语音数据
     */
    private void processVoiceData(PlayerData sender, MicPacket micPacket) {
        // 使用发送者的UUID作为channelId，这样客户端可以正确识别说话者
        UUID channelId = sender.getUuid();



        // 创建玩家音频包（使用正确的PlayerSoundPacket）
        PlayerSoundPacket soundPacket = new PlayerSoundPacket(
                channelId,          // channelId - 使用发送者UUID
                sender.getUuid(),   // sender
                micPacket.getData(), // 音频数据
                micPacket.getSequenceNumber(), // 序列号
                micPacket.isWhispering(), // 是否耳语
                (float) config.getVoice().getMaxDistance(), // 距离
                "proximity" // 类别
        );



        // 广播给附近的玩家
        broadcastToNearbyPlayers(sender, soundPacket, micPacket.isWhispering());
    }
    
    /**
     * 广播音频到附近玩家
     */
    private void broadcastToNearbyPlayers(PlayerData sender, PlayerSoundPacket soundPacket, boolean whispering) {
        double maxDistance = config.getVoice().getMaxDistance();
        if (whispering) {
            maxDistance *= 0.5; // 悄悄话距离减半
        }


        
        for (PlayerData receiver : playerManager.getAllPlayers()) {
            if (receiver.getUuid().equals(sender.getUuid())) {
                continue; // 不发送给自己
            }
            
            if (!receiver.isVoiceConnected() || receiver.isVoiceDisabled()) {
                continue; // 跳过未连接或禁用语音的玩家
            }
            
            if (!sender.isInSameWorld(receiver)) {
                continue; // 不在同一世界
            }
            
            if (!sender.isWithinVoiceRange(receiver, maxDistance)) {
                continue; // 超出语音范围
            }
            
            // 发送音频包
            ClientConnection connection = connections.get(receiver.getUuid());
            if (connection != null) {
                try {
                    sendPacket(soundPacket, connection.getAddress());
                } catch (Exception e) {
                    LOGGER.warn("Failed to send sound packet to {}", receiver.getName(), e);
                }
            }
        }
    }

    /**
     * 广播群组音频到群组成员
     */
    private void broadcastToGroupMembers(PlayerData sender, GroupSoundPacket soundPacket) {
        // 获取发送者所在的群组
        UUID groupId = sender.getGroupId();
        if (groupId == null) {
            return;
        }

        GroupManager.VoiceGroup group = groupManager.getGroup(groupId);
        if (group == null) {
            return;
        }

        // 广播给群组中的其他成员
        int sentCount = 0;
        for (UUID memberId : group.getMembers()) {
            if (memberId.equals(sender.getUuid())) {
                continue; // 不发送给自己
            }

            PlayerData member = playerManager.getPlayer(memberId);
            if (member == null || !member.isVoiceConnected() || member.isVoiceDisabled()) {
                continue;
            }

            ClientConnection connection = connections.get(memberId);
            if (connection != null) {
                try {
                    sendPacket(soundPacket, connection.getAddress());
                    sentCount++;
                } catch (Exception e) {
                    LOGGER.warn("Failed to send group sound packet to {}", member.getName(), e);
                }
            }
        }
    }

    /**
     * 广播位置音频到指定位置附近的玩家
     */
    private void broadcastToLocationNearbyPlayers(PlayerData sender, LocationSoundPacket soundPacket) {
        double soundX = soundPacket.getX();
        double soundY = soundPacket.getY();
        double soundZ = soundPacket.getZ();
        double maxDistance = soundPacket.getDistance();

        for (PlayerData receiver : playerManager.getAllPlayers()) {
            if (receiver.getUuid().equals(sender.getUuid())) {
                continue; // 不发送给自己
            }

            if (!receiver.isVoiceConnected() || receiver.isVoiceDisabled()) {
                continue;
            }

            if (!sender.isInSameWorld(receiver)) {
                continue; // 不在同一世界
            }

            // 计算到声音位置的距离
            if (receiver.getPosition() == null) {
                continue; // 没有位置信息
            }

            double distance = Math.sqrt(
                Math.pow(receiver.getPosition().getX() - soundX, 2) +
                Math.pow(receiver.getPosition().getY() - soundY, 2) +
                Math.pow(receiver.getPosition().getZ() - soundZ, 2)
            );

            if (distance > maxDistance) {
                continue; // 超出音频范围
            }

            ClientConnection connection = connections.get(receiver.getUuid());
            if (connection != null) {
                try {
                    sendPacket(soundPacket, connection.getAddress());
                } catch (Exception e) {
                    LOGGER.warn("Failed to send location sound packet to {}", receiver.getName(), e);
                }
            }
        }
    }

    /**
     * 支持群组语音的语音数据处理（根据群组类型）
     */
    private void processGroupVoiceData(PlayerData sender, MicPacket micPacket) {
        UUID groupId = sender.getGroupId();
        if (groupId == null) {
            // 如果不在群组中，使用普通的附近语音
            processVoiceData(sender, micPacket);
            return;
        }

        GroupManager.VoiceGroup group = groupManager.getGroup(groupId);
        if (group == null) {
            LOGGER.warn("Player {} is in non-existent group {}", sender.getName(), groupId);
            processVoiceData(sender, micPacket);
            return;
        }

        // 创建群组音频包（与原版一致：使用发送者UUID作为channelId）
        GroupSoundPacket groupSoundPacket = new GroupSoundPacket(
                sender.getUuid(), // channelId使用发送者UUID（与原版一致）
                sender.getUuid(), // sender
                micPacket.getData(), // 音频数据
                micPacket.getSequenceNumber(), // 序列号
                "group" // 类别
        );



        // 根据群组类型决定广播策略
        switch (group.getType()) {
            case NORMAL:
                // 普通群组：只广播给群组成员
                broadcastToGroupMembers(sender, groupSoundPacket);
                break;

            case OPEN:
                // 开放群组：广播给群组成员 + 附近玩家
                broadcastToGroupMembers(sender, groupSoundPacket);
                broadcastToNearbyNonGroupPlayers(sender, micPacket);
                break;

            case ISOLATED:
                // 隔离群组：只广播给群组成员（与NORMAL相同）
                broadcastToGroupMembers(sender, groupSoundPacket);
                break;

            default:
                LOGGER.warn("Unknown group type: {} for group {}", group.getType(), groupId);
                broadcastToGroupMembers(sender, groupSoundPacket);
                break;
        }
    }

    /**
     * 广播给附近的非群组玩家（用于开放群组）
     */
    private void broadcastToNearbyNonGroupPlayers(PlayerData sender, MicPacket micPacket) {
        // 创建玩家音频包
        PlayerSoundPacket soundPacket = new PlayerSoundPacket(
                UUID.randomUUID(), // channelId - 可以是默认频道
                sender.getUuid(),   // sender
                micPacket.getData(), // 音频数据
                micPacket.getSequenceNumber(), // 序列号
                micPacket.isWhispering(), // 是否耳语
                (float) config.getVoice().getMaxDistance(), // 距离
                "proximity" // 类别
        );

        // 广播给附近的非群组玩家
        for (PlayerData receiver : playerManager.getAllPlayers()) {
            if (receiver.getUuid().equals(sender.getUuid())) {
                continue; // 不发送给自己
            }

            if (!receiver.isVoiceConnected() || receiver.isVoiceDisabled()) {
                continue;
            }

            if (!sender.isInSameWorld(receiver)) {
                continue; // 不在同一世界
            }

            // 跳过群组成员（他们已经通过群组音频包收到了）
            if (receiver.getGroupId() != null && receiver.getGroupId().equals(sender.getGroupId())) {
                continue;
            }

            // 检查距离
            if (sender.getPosition() != null && receiver.getPosition() != null) {
                double distance = sender.getPosition().getDistanceTo(receiver.getPosition());
                double maxDistance = micPacket.isWhispering() ?
                    config.getVoice().getWhisperDistance() :
                    config.getVoice().getMaxDistance();

                if (distance > maxDistance) {
                    continue; // 超出语音范围
                }
            }

            ClientConnection connection = connections.get(receiver.getUuid());
            if (connection != null) {
                try {
                    sendPacket(soundPacket, connection.getAddress());
                } catch (Exception e) {
                    LOGGER.warn("Failed to send sound packet to {}", receiver.getName(), e);
                }
            }
        }
    }

    // Getters
    public PlayerManager getPlayerManager() { return playerManager; }
    public GroupManager getGroupManager() { return groupManager; }
    public ConnectionManager getConnectionManager() { return connectionManager; }

    /**
     * 检查玩家是否连接到语音聊天
     */
    public boolean isPlayerConnected(UUID playerUuid) {
        return connections.containsKey(playerUuid);
    }

    /**
     * 获取连接映射（用于ConnectionManager）
     */
    public ConcurrentHashMap<UUID, ClientConnection> getConnections() {
        return connections;
    }
    
    /**
     * 语音服务器网络处理器
     */
    private class VoiceServerHandler extends SimpleChannelInboundHandler<DatagramPacket> {
        
        @Override
        protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket packet) {
            try {
                byte[] data = new byte[packet.content().readableBytes()];
                packet.content().readBytes(data);
                
                NetworkMessage message = NetworkMessage.fromBytes(data, packet.sender(), playerManager);
                if (message != null) {
                    handleMessage(message);
                }
                // 如果message为null，说明是未知数据包，已经在NetworkMessage中记录了警告
                
            } catch (Exception e) {
                LOGGER.warn("Failed to process packet from {}", packet.sender(), e);
            }
        }
        
        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            LOGGER.error("Voice server handler error", cause);
        }
    }
}
