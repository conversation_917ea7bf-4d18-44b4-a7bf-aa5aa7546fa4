package de.maxhenkel.voicechat.standalone;

import de.maxhenkel.voicechat.standalone.config.ServerConfig;
import de.maxhenkel.voicechat.standalone.server.VoiceServer;
import de.maxhenkel.voicechat.standalone.api.ApiServer;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 语音服务器集成测试
 */
public class VoiceServerIntegrationTest {
    
    private ServerConfig config;
    private VoiceServer voiceServer;
    private ApiServer apiServer;
    private HttpClient httpClient;
    
    @BeforeEach
    void setUp() throws Exception {
        // 创建测试配置
        config = createTestConfig();
        
        // 启动语音服务器
        voiceServer = new VoiceServer(config);
        voiceServer.start();
        
        // 启动API服务器
        apiServer = new ApiServer(config, voiceServer);
        apiServer.start();
        
        // 创建HTTP客户端
        httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        
        // 等待服务器启动
        Thread.sleep(2000);
    }
    
    @AfterEach
    void tearDown() throws Exception {
        if (apiServer != null) {
            apiServer.stop();
        }
        if (voiceServer != null) {
            voiceServer.stop();
        }
    }
    
    @Test
    void testHealthCheck() throws Exception {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://localhost:8081/health"))
                .GET()
                .build();
        
        HttpResponse<String> response = httpClient.send(request, 
                HttpResponse.BodyHandlers.ofString());
        
        assertEquals(200, response.statusCode());
        assertTrue(response.body().contains("healthy"));
    }
    
    @Test
    void testPlayerLogin() throws Exception {
        String requestBody = """
                {
                    "uuid": "550e8400-e29b-41d4-a716-************",
                    "name": "TestPlayer",
                    "serverName": "testserver",
                    "permissions": ["voicechat.speak", "voicechat.groups"],
                    "position": {
                        "worldId": "world",
                        "x": 0.0,
                        "y": 64.0,
                        "z": 0.0
                    }
                }
                """;
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://localhost:8081/api/players/login"))
                .header("Authorization", "Bearer test-token")
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();
        
        HttpResponse<String> response = httpClient.send(request, 
                HttpResponse.BodyHandlers.ofString());
        
        assertEquals(200, response.statusCode());
        assertTrue(response.body().contains("success"));
    }
    
    @Test
    void testPlayerLogout() throws Exception {
        // 先登录玩家
        testPlayerLogin();
        
        String requestBody = """
                {
                    "uuid": "550e8400-e29b-41d4-a716-************"
                }
                """;
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://localhost:8081/api/players/logout"))
                .header("Authorization", "Bearer test-token")
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();
        
        HttpResponse<String> response = httpClient.send(request, 
                HttpResponse.BodyHandlers.ofString());
        
        assertEquals(200, response.statusCode());
        assertTrue(response.body().contains("success"));
    }
    
    @Test
    void testUnauthorizedAccess() throws Exception {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://localhost:8081/api/status"))
                .GET()
                .build();
        
        HttpResponse<String> response = httpClient.send(request, 
                HttpResponse.BodyHandlers.ofString());
        
        assertEquals(401, response.statusCode());
    }
    
    @Test
    void testServerStatus() throws Exception {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://localhost:8081/api/status"))
                .header("Authorization", "Bearer test-token")
                .GET()
                .build();
        
        HttpResponse<String> response = httpClient.send(request, 
                HttpResponse.BodyHandlers.ofString());
        
        assertEquals(200, response.statusCode());
        assertTrue(response.body().contains("voiceServer"));
    }
    
    @Test
    void testCreateGroup() throws Exception {
        String requestBody = """
                {
                    "name": "TestGroup",
                    "password": "testpass",
                    "ownerUuid": "550e8400-e29b-41d4-a716-************",
                    "persistent": false,
                    "open": true
                }
                """;
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://localhost:8081/api/groups"))
                .header("Authorization", "Bearer test-token")
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();
        
        HttpResponse<String> response = httpClient.send(request, 
                HttpResponse.BodyHandlers.ofString());
        
        assertEquals(201, response.statusCode());
        assertTrue(response.body().contains("TestGroup"));
    }
    
    /**
     * 创建测试配置
     */
    private ServerConfig createTestConfig() throws Exception {
        // 创建临时配置文件
        File tempConfig = File.createTempFile("test-config", ".yml");
        tempConfig.deleteOnExit();
        
        String configContent = """
                server:
                  host: "localhost"
                  port: 24455
                  bind_address: ""
                
                api:
                  host: "localhost"
                  port: 8081
                  auth_token: "test-token"
                
                minecraft:
                  servers:
                    - name: "testserver"
                      host: "localhost"
                      api_endpoint: "http://localhost:8082/voicechat"
                
                voice:
                  codec: "VOIP"
                  mtu_size: 1024
                  keep_alive: 1000
                  max_distance: 48.0
                  groups_enabled: true
                  allow_recording: true
                
                security:
                  encryption_enabled: true
                  auth_timeout: 30000
                """;
        
        java.nio.file.Files.write(tempConfig.toPath(), configContent.getBytes());
        
        return ServerConfig.load(tempConfig);
    }
}
