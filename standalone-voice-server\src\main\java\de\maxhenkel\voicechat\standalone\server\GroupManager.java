package de.maxhenkel.voicechat.standalone.server;

import de.maxhenkel.voicechat.standalone.model.GroupType;
import de.maxhenkel.voicechat.standalone.model.PlayerData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 群组管理器
 */
public class GroupManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(GroupManager.class);
    
    private final ConcurrentHashMap<UUID, VoiceGroup> groups = new ConcurrentHashMap<>();
    
    /**
     * 语音群组类
     */
    public static class VoiceGroup {
        private final UUID id;
        private final String name;
        private final String password;
        private final UUID owner;
        private final Set<UUID> members = ConcurrentHashMap.newKeySet();
        private final long createdTime;
        private boolean persistent;
        private boolean open;
        private GroupType type; // 群组类型
        
        public VoiceGroup(UUID id, String name, String password, UUID owner) {
            this(id, name, password, owner, GroupType.NORMAL);
        }

        public VoiceGroup(UUID id, String name, String password, UUID owner, GroupType type) {
            this.id = id;
            this.name = name;
            this.password = password;
            this.owner = owner;
            this.type = type;
            this.createdTime = System.currentTimeMillis();
            this.persistent = false;
            this.open = true;
        }
        
        // Getters and Setters
        public UUID getId() { return id; }
        public String getName() { return name; }
        public String getPassword() { return password; }
        public UUID getOwner() { return owner; }
        public Set<UUID> getMembers() { return new HashSet<>(members); }
        public long getCreatedTime() { return createdTime; }
        public boolean isPersistent() { return persistent; }
        public void setPersistent(boolean persistent) { this.persistent = persistent; }
        public boolean isOpen() { return open; }
        public void setOpen(boolean open) { this.open = open; }
        public GroupType getType() { return type; }
        public void setType(GroupType type) { this.type = type; }
        
        public boolean hasPassword() { return password != null && !password.isEmpty(); }
        public boolean checkPassword(String inputPassword) {
            return !hasPassword() || password.equals(inputPassword);
        }
        
        public void addMember(UUID playerUuid) { members.add(playerUuid); }
        public void removeMember(UUID playerUuid) { members.remove(playerUuid); }
        public boolean hasMember(UUID playerUuid) { return members.contains(playerUuid); }
        public int getMemberCount() { return members.size(); }
        public boolean isEmpty() { return members.isEmpty(); }
    }
    
    /**
     * 创建群组
     */
    public VoiceGroup createGroup(String name, String password, UUID owner) {
        return createGroup(name, password, owner, GroupType.NORMAL);
    }

    /**
     * 创建群组（指定类型）
     */
    public VoiceGroup createGroup(String name, String password, UUID owner, GroupType type) {
        UUID groupId = UUID.randomUUID();
        VoiceGroup group = new VoiceGroup(groupId, name, password, owner, type);
        groups.put(groupId, group);

        LOGGER.info("Created group '{}' with ID {} by {} (type: {})", name, groupId, owner, type);
        return group;
    }
    
    /**
     * 删除群组
     */
    public boolean deleteGroup(UUID groupId) {
        VoiceGroup group = groups.remove(groupId);
        if (group != null) {
            LOGGER.info("Deleted group '{}' with ID {}", group.getName(), groupId);
            return true;
        }
        return false;
    }
    
    /**
     * 获取群组
     */
    public VoiceGroup getGroup(UUID groupId) {
        return groups.get(groupId);
    }
    
    /**
     * 根据名称获取群组
     */
    public VoiceGroup getGroupByName(String name) {
        return groups.values().stream()
                .filter(group -> group.getName().equalsIgnoreCase(name))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取所有群组
     */
    public Collection<VoiceGroup> getAllGroups() {
        return groups.values();
    }
    
    /**
     * 玩家加入群组
     */
    public boolean joinGroup(UUID groupId, UUID playerUuid, String password) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }
        
        if (!group.checkPassword(password)) {
            LOGGER.warn("Player {} failed to join group '{}' - wrong password", playerUuid, group.getName());
            return false;
        }
        
        group.addMember(playerUuid);
        LOGGER.info("Player {} joined group '{}'", playerUuid, group.getName());
        return true;
    }
    
    /**
     * 玩家离开群组
     */
    public boolean leaveGroup(UUID groupId, UUID playerUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }
        
        group.removeMember(playerUuid);
        LOGGER.info("Player {} left group '{}'", playerUuid, group.getName());
        
        // 如果群组为空且不是持久化群组，则删除
        if (group.isEmpty() && !group.isPersistent()) {
            deleteGroup(groupId);
        }
        
        return true;
    }
    
    /**
     * 获取玩家所在的群组
     */
    public VoiceGroup getPlayerGroup(UUID playerUuid) {
        return groups.values().stream()
                .filter(group -> group.hasMember(playerUuid))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取玩家创建的群组
     */
    public Collection<VoiceGroup> getPlayerOwnedGroups(UUID playerUuid) {
        return groups.values().stream()
                .filter(group -> group.getOwner().equals(playerUuid))
                .toList();
    }
    
    /**
     * 踢出群组成员
     */
    public boolean kickMember(UUID groupId, UUID memberUuid, UUID kickerUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }
        
        // 只有群组所有者可以踢人
        if (!group.getOwner().equals(kickerUuid)) {
            return false;
        }
        
        if (group.hasMember(memberUuid)) {
            group.removeMember(memberUuid);
            LOGGER.info("Player {} was kicked from group '{}' by {}", memberUuid, group.getName(), kickerUuid);
            return true;
        }
        
        return false;
    }
    
    /**
     * 转移群组所有权
     */
    public boolean transferOwnership(UUID groupId, UUID newOwnerUuid, UUID currentOwnerUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }
        
        // 只有当前所有者可以转移所有权
        if (!group.getOwner().equals(currentOwnerUuid)) {
            return false;
        }
        
        // 新所有者必须是群组成员
        if (!group.hasMember(newOwnerUuid)) {
            return false;
        }
        
        // 创建新的群组对象（因为owner是final的）
        VoiceGroup newGroup = new VoiceGroup(group.getId(), group.getName(), group.getPassword(), newOwnerUuid);
        newGroup.setPersistent(group.isPersistent());
        newGroup.setOpen(group.isOpen());
        group.getMembers().forEach(newGroup::addMember);
        
        groups.put(groupId, newGroup);
        LOGGER.info("Group '{}' ownership transferred from {} to {}", group.getName(), currentOwnerUuid, newOwnerUuid);
        return true;
    }
    
    /**
     * 清理空的非持久化群组
     */
    public void cleanupEmptyGroups() {
        groups.entrySet().removeIf(entry -> {
            VoiceGroup group = entry.getValue();
            if (group.isEmpty() && !group.isPersistent()) {
                LOGGER.debug("Cleaned up empty group: {}", group.getName());
                return true;
            }
            return false;
        });
    }
    
    /**
     * 获取群组统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalGroups", groups.size());
        stats.put("persistentGroups", groups.values().stream()
                .mapToInt(group -> group.isPersistent() ? 1 : 0)
                .sum());
        stats.put("totalMembers", groups.values().stream()
                .mapToInt(VoiceGroup::getMemberCount)
                .sum());
        return stats;
    }
}
