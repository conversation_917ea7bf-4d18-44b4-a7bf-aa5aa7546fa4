package de.maxhenkel.voicechat.adapter.commands;

import de.maxhenkel.voicechat.adapter.VoiceChatAdapterPlugin;
import de.maxhenkel.voicechat.adapter.network.VoiceServerClient;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 语音聊天命令处理器
 */
public class VoiceChatCommand implements CommandExecutor, TabCompleter {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceChatCommand.class);
    
    private final VoiceChatAdapterPlugin plugin;
    private final VoiceServerClient voiceServerClient;
    
    public VoiceChatCommand(VoiceChatAdapterPlugin plugin, VoiceServerClient voiceServerClient) {
        this.plugin = plugin;
        this.voiceServerClient = voiceServerClient;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "status":
                handleStatusCommand(sender);
                break;
            case "reload":
                handleReloadCommand(sender);
                break;
            case "secret":
                handleSecretCommand(sender, args);
                break;
            case "help":
                sendHelp(sender);
                break;
            case "syncgroups":
                handleSyncGroupsCommand(sender);
                break;
            default:
                sender.sendMessage(ChatColor.RED + "Unknown subcommand: " + subCommand);
                sendHelp(sender);
                break;
        }
        
        return true;
    }
    
    /**
     * 处理状态命令
     */
    private void handleStatusCommand(CommandSender sender) {
        if (!sender.hasPermission("voicechat.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return;
        }
        
        sender.sendMessage(ChatColor.YELLOW + "Fetching voice server status...");
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    Map<String, Object> status = voiceServerClient.getServerStatus();
                    
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.GREEN + "=== Voice Server Status ===");
                            
                            Map<String, Object> voiceServer = (Map<String, Object>) status.get("voiceServer");
                            if (voiceServer != null) {
                                Map<String, Object> players = (Map<String, Object>) voiceServer.get("players");
                                Map<String, Object> groups = (Map<String, Object>) voiceServer.get("groups");
                                
                                if (players != null) {
                                    sender.sendMessage(ChatColor.AQUA + "Players:");
                                    sender.sendMessage("  Total: " + players.get("totalPlayers"));
                                    sender.sendMessage("  Online: " + players.get("onlinePlayers"));
                                    sender.sendMessage("  Voice Connected: " + players.get("voiceConnectedPlayers"));
                                }
                                
                                if (groups != null) {
                                    sender.sendMessage(ChatColor.AQUA + "Groups:");
                                    sender.sendMessage("  Total: " + groups.get("totalGroups"));
                                    sender.sendMessage("  Members: " + groups.get("totalMembers"));
                                }
                            }
                            
                        }
                    }.runTask(plugin);
                    
                } catch (Exception e) {
                    LOGGER.error("Failed to get voice server status", e);
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.RED + "Failed to get voice server status: " + e.getMessage());
                        }
                    }.runTask(plugin);
                }
            }
        }.runTaskAsynchronously(plugin);
    }
    
    /**
     * 处理重载命令
     */
    private void handleReloadCommand(CommandSender sender) {
        if (!sender.hasPermission("voicechat.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return;
        }
        
        sender.sendMessage(ChatColor.YELLOW + "Reloading voice chat adapter configuration...");
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    plugin.reloadConfiguration();
                    
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.GREEN + "Configuration reloaded successfully!");
                        }
                    }.runTask(plugin);
                    
                } catch (Exception e) {
                    LOGGER.error("Failed to reload configuration", e);
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.RED + "Failed to reload configuration: " + e.getMessage());
                        }
                    }.runTask(plugin);
                }
            }
        }.runTaskAsynchronously(plugin);
    }
    
    /**
     * 处理密钥生成命令
     */
    private void handleSecretCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("voicechat.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return;
        }
        
        Player targetPlayer;
        if (args.length > 1) {
            targetPlayer = plugin.getServer().getPlayer(args[1]);
            if (targetPlayer == null) {
                sender.sendMessage(ChatColor.RED + "Player not found: " + args[1]);
                return;
            }
        } else if (sender instanceof Player) {
            targetPlayer = (Player) sender;
        } else {
            sender.sendMessage(ChatColor.RED + "You must specify a player name when using this command from console.");
            return;
        }
        
        sender.sendMessage(ChatColor.YELLOW + "Generating voice chat secret for " + targetPlayer.getName() + "...");
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    String secret = voiceServerClient.generatePlayerSecret(targetPlayer.getUniqueId());
                    
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.GREEN + "Voice chat secret for " + targetPlayer.getName() + ": " + secret);
                            if (!sender.equals(targetPlayer)) {
                                targetPlayer.sendMessage(ChatColor.GREEN + "Your voice chat secret: " + secret);
                            }
                        }
                    }.runTask(plugin);
                    
                } catch (Exception e) {
                    LOGGER.error("Failed to generate secret for {}", targetPlayer.getName(), e);
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.RED + "Failed to generate secret: " + e.getMessage());
                        }
                    }.runTask(plugin);
                }
            }
        }.runTaskAsynchronously(plugin);
    }

    /**
     * 处理群组同步命令
     */
    private void handleSyncGroupsCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return;
        }

        Player player = (Player) sender;

        try {
            plugin.getGroupMessageHandler().syncGroupsForPlayer(player);
            sender.sendMessage(ChatColor.GREEN + "Group synchronization started. Check console for details.");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Failed to sync groups: " + e.getMessage());
            LOGGER.error("Failed to sync groups for player {}", player.getName(), e);
        }
    }

    /**
     * 发送帮助信息
     */
    private void sendHelp(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== Voice Chat Adapter Commands ===");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat status" + ChatColor.WHITE + " - Show voice server status");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat reload" + ChatColor.WHITE + " - Reload configuration");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat secret [player]" + ChatColor.WHITE + " - Generate voice chat secret");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat syncgroups" + ChatColor.WHITE + " - Manually sync voice chat groups");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat help" + ChatColor.WHITE + " - Show this help message");
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            List<String> subCommands = Arrays.asList("status", "reload", "secret", "help");
            String partial = args[0].toLowerCase();
            
            for (String subCommand : subCommands) {
                if (subCommand.startsWith(partial)) {
                    completions.add(subCommand);
                }
            }
        } else if (args.length == 2 && args[0].equalsIgnoreCase("secret")) {
            String partial = args[1].toLowerCase();
            for (Player player : plugin.getServer().getOnlinePlayers()) {
                if (player.getName().toLowerCase().startsWith(partial)) {
                    completions.add(player.getName());
                }
            }
        }
        
        return completions;
    }
}
