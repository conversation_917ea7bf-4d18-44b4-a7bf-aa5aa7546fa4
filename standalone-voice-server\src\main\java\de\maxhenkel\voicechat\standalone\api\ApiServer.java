package de.maxhenkel.voicechat.standalone.api;

import de.maxhenkel.voicechat.standalone.config.ServerConfig;
import de.maxhenkel.voicechat.standalone.server.VoiceServer;
import io.javalin.Javalin;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * API服务器，处理与Minecraft服务器的HTTP通信
 */
public class ApiServer {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiServer.class);
    
    private final ServerConfig config;
    private final VoiceServer voiceServer;
    private final PlayerApiHandler playerApiHandler;
    private final GroupApiHandler groupApiHandler;
    private final ConfigApiHandler configApiHandler;
    
    private Javalin app;
    private final long startTime; // 服务器启动时间

    public ApiServer(ServerConfig config, VoiceServer voiceServer) {
        this.config = config;
        this.voiceServer = voiceServer;
        this.playerApiHandler = new PlayerApiHandler(voiceServer);
        this.groupApiHandler = new GroupApiHandler(voiceServer);
        this.configApiHandler = new ConfigApiHandler(config);
        this.startTime = System.currentTimeMillis(); // 记录启动时间
    }
    
    /**
     * 启动API服务器
     */
    public void start() {
        app = Javalin.create(javalinConfig -> {
            javalinConfig.showJavalinBanner = false;
            javalinConfig.jsonMapper(new JacksonJsonMapper());
        });
        
        // 添加认证中间件
        app.before(this::authenticate);
        
        // 注册路由
        registerRoutes();
        
        // 启动服务器
        app.start(config.getApi().getHost(), config.getApi().getPort());
        
        LOGGER.info("API server started on {}:{}", config.getApi().getHost(), config.getApi().getPort());
    }
    
    /**
     * 停止API服务器
     */
    public void stop() {
        if (app != null) {
            app.stop();
            LOGGER.info("API server stopped");
        }
    }
    
    /**
     * 认证中间件
     */
    private void authenticate(Context ctx) {
        // 跳过健康检查端点
        if (ctx.path().equals("/health")) {
            return;
        }
        
        String authHeader = ctx.header("Authorization");
        String expectedToken = "Bearer " + config.getApi().getAuthToken();
        
        if (authHeader == null || !authHeader.equals(expectedToken)) {
            ctx.status(HttpStatus.UNAUTHORIZED)
               .json(Map.of("error", "Unauthorized", "message", "Invalid or missing authorization token"));
        }
    }
    
    /**
     * 注册API路由
     */
    private void registerRoutes() {
        // 健康检查
        app.get("/health", ctx -> {
            ctx.json(Map.of(
                "status", "healthy",
                "timestamp", System.currentTimeMillis(),
                "startTime", startTime, // 添加启动时间
                "uptime", System.currentTimeMillis() - startTime, // 运行时间
                "version", "1.0.0"
            ));
        });
        
        // 服务器状态
        app.get("/api/status", ctx -> {
            ctx.json(Map.of(
                "voiceServer", Map.of(
                    "running", true,
                    "players", voiceServer.getPlayerManager().getStatistics(),
                    "groups", voiceServer.getGroupManager().getStatistics(),
                    "connections", voiceServer.getConnectionManager().getConnectionStatistics()
                )
            ));
        });
        
        // 玩家管理API
        app.post("/api/players/login", playerApiHandler::handlePlayerLogin);
        app.post("/api/players/logout", playerApiHandler::handlePlayerLogout);
        app.put("/api/players/{uuid}/position", playerApiHandler::handleUpdatePosition);
        app.put("/api/players/{uuid}/permissions", playerApiHandler::handleUpdatePermissions);
        app.get("/api/players/{uuid}/state", playerApiHandler::handleGetPlayerState);
        app.get("/api/players", playerApiHandler::handleGetAllPlayers);
        app.get("/api/players/connections", playerApiHandler::handleGetPlayerConnections);
        app.post("/api/players/{uuid}/secret", playerApiHandler::handleGenerateSecret);
        
        // 群组管理API
        app.post("/api/groups", groupApiHandler::handleCreateGroup);
        app.delete("/api/groups/{groupId}", groupApiHandler::handleDeleteGroup);
        app.post("/api/groups/{groupId}/members", groupApiHandler::handleJoinGroup);
        app.delete("/api/groups/{groupId}/members/{playerId}", groupApiHandler::handleLeaveGroup);
        app.delete("/api/players/{playerId}/group", groupApiHandler::handlePlayerLeaveGroup);
        app.get("/api/groups", groupApiHandler::handleGetAllGroups);
        app.get("/api/groups/{groupId}", groupApiHandler::handleGetGroup);
        
        // 配置管理API
        app.get("/api/config", configApiHandler::handleGetConfig);
        app.put("/api/config", configApiHandler::handleUpdateConfig);
        
        // 错误处理
        app.exception(Exception.class, (e, ctx) -> {
            LOGGER.error("API error", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of(
                   "error", "Internal Server Error",
                   "message", e.getMessage()
               ));
        });
        
        // 404处理
        app.error(HttpStatus.NOT_FOUND, ctx -> {
            ctx.json(Map.of(
                "error", "Not Found",
                "message", "The requested endpoint was not found"
            ));
        });
    }
}
