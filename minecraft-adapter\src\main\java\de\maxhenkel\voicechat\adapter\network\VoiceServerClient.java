package de.maxhenkel.voicechat.adapter.network;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.maxhenkel.voicechat.adapter.config.AdapterConfig;
import de.maxhenkel.voicechat.adapter.model.PlayerInfo;
import de.maxhenkel.voicechat.adapter.model.Position;
import okhttp3.*;
import org.bukkit.entity.Player;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 语音服务器HTTP客户端
 */
public class VoiceServerClient {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceServerClient.class);
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    private final AdapterConfig config;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final String baseUrl;
    private final String authHeader;
    private volatile boolean connected = false;
    private volatile long serverStartTime = 0; // 服务器启动时间，用于检测重启
    
    public VoiceServerClient(AdapterConfig config) {
        this.config = config;
        this.baseUrl = config.getVoiceServer().getApiEndpoint();
        this.authHeader = "Bearer " + config.getVoiceServer().getAuthToken();
        this.objectMapper = new ObjectMapper();
        
        // 配置HTTP客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 初始化客户端
     */
    public void initialize() throws Exception {
        // 测试连接
        if (!testConnection()) {
            throw new Exception("Failed to connect to voice server");
        }
        connected = true;
        LOGGER.info("Voice server client initialized successfully");
    }
    
    /**
     * 检查是否已连接
     */
    public boolean isConnected() {
        return connected;
    }

    /**
     * 检查服务器是否重启并返回是否需要重新同步
     */
    public boolean checkServerRestart() {
        try {
            long oldStartTime = serverStartTime;
            testConnectionAndCheckRestart();
            return oldStartTime > 0 && serverStartTime > oldStartTime;
        } catch (Exception e) {
            LOGGER.warn("Failed to check server restart: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有群组列表
     */
    public java.util.List<Map<String, Object>> getAllGroups() {
        try {
            String url = config.getVoiceServer().getApiEndpoint() + "/api/groups";
            LOGGER.debug("Requesting all groups from URL: {}", url);

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                    .header("Content-Type", "application/json")
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                LOGGER.debug("Response code: {}, successful: {}", response.code(), response.isSuccessful());

                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    LOGGER.debug("Response body: {}", responseBody);

                    if (responseBody.trim().isEmpty()) {
                        LOGGER.warn("Empty response body from voice server");
                        return new java.util.ArrayList<>();
                    }

                    java.util.List<Map<String, Object>> groups = objectMapper.readValue(responseBody, java.util.List.class);
                    LOGGER.debug("Parsed {} groups from response", groups.size());
                    return groups;
                } else {
                    String responseBody = response.body() != null ? response.body().string() : "No response body";
                    LOGGER.warn("Failed to get all groups: HTTP {} - {}", response.code(), responseBody);
                    return new java.util.ArrayList<>(); // 返回空列表而不是null
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error getting all groups: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取群组详细信息
     */
    public Map<String, Object> getGroupInfo(UUID groupUuid) {
        try {
            String url = config.getVoiceServer().getApiEndpoint() + "/api/groups/" + groupUuid.toString();

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                    .header("Content-Type", "application/json")
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    return objectMapper.readValue(responseBody, Map.class);
                } else {
                    LOGGER.warn("Failed to get group info {}: HTTP {}", groupUuid, response.code());
                    return null;
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error getting group info {}: {}", groupUuid, e.getMessage());
            return null;
        }
    }

    /**
     * 重新同步所有在线玩家（用于服务器重启后）
     */
    public void resyncAllPlayers(java.util.Collection<? extends org.bukkit.entity.Player> onlinePlayers) {
        LOGGER.info("Resyncing {} online players to voice server", onlinePlayers.size());

        for (org.bukkit.entity.Player player : onlinePlayers) {
            try {
                // 重新发送玩家登录信息
                de.maxhenkel.voicechat.adapter.model.PlayerInfo playerInfo = createPlayerInfo(player);
                playerLogin(playerInfo);

                LOGGER.debug("Resynced player {} to voice server", player.getName());

                // 添加小延迟避免过快发送
                Thread.sleep(50);

            } catch (Exception e) {
                LOGGER.error("Failed to resync player {}: {}", player.getName(), e.getMessage());
            }
        }

        LOGGER.info("Completed resyncing all players to voice server");
    }

    /**
     * 创建玩家信息（从Player对象）
     */
    private de.maxhenkel.voicechat.adapter.model.PlayerInfo createPlayerInfo(org.bukkit.entity.Player player) {
        de.maxhenkel.voicechat.adapter.model.PlayerInfo playerInfo =
            new de.maxhenkel.voicechat.adapter.model.PlayerInfo(player.getUniqueId(), player.getName());

        // 设置位置
        de.maxhenkel.voicechat.adapter.model.Position position =
            new de.maxhenkel.voicechat.adapter.model.Position(
                player.getWorld().getName(),
                player.getLocation().getX(),
                player.getLocation().getY(),
                player.getLocation().getZ()
            );
        playerInfo.setPosition(position);

        // 设置权限
        java.util.List<String> permissions = new java.util.ArrayList<>();
        if (player.hasPermission("voicechat.speak")) {
            permissions.add("voicechat.speak");
        }
        if (player.hasPermission("voicechat.groups")) {
            permissions.add("voicechat.groups");
        }
        if (player.hasPermission("voicechat.admin")) {
            permissions.add("voicechat.admin");
        }
        if (player.hasPermission("voicechat.record")) {
            permissions.add("voicechat.record");
        }
        playerInfo.setPermissions(permissions);

        return playerInfo;
    }

    /**
     * 获取玩家连接状态
     */
    public Map<UUID, Boolean> getPlayerConnectionStates() throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/connections")
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to get player connections: " + response.code());
            }

            String responseBody = response.body().string();
            @SuppressWarnings("unchecked")
            Map<String, Boolean> rawMap = objectMapper.readValue(responseBody, Map.class);

            Map<UUID, Boolean> connectionStates = new HashMap<>();
            for (Map.Entry<String, Boolean> entry : rawMap.entrySet()) {
                try {
                    UUID playerUuid = UUID.fromString(entry.getKey());
                    connectionStates.put(playerUuid, entry.getValue());
                } catch (IllegalArgumentException e) {
                    LOGGER.warn("Invalid UUID in connection states: {}", entry.getKey());
                }
            }

            return connectionStates;
        }
    }

    /**
     * 测试与独立服务端的连接
     */
    public boolean testConnection() {
        try {
            Request request = new Request.Builder()
                    .url(baseUrl + "/api/health")
                    .header("Authorization", authHeader)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                boolean isHealthy = response.isSuccessful();
                if (isHealthy != connected) {
                    connected = isHealthy;
                    if (isHealthy) {
                        LOGGER.info("Connection to voice server restored");
                    } else {
                        LOGGER.warn("Connection to voice server lost");
                    }
                }
                return isHealthy;
            }
        } catch (Exception e) {
            if (connected) {
                LOGGER.warn("Connection test failed: {}", e.getMessage());
                connected = false;
            }
            return false;
        }
    }

    /**
     * 安全执行HTTP请求，带有异常处理和重试
     */
    private <T> T executeWithRetry(String operation, HttpRequestExecutor<T> executor) throws Exception {
        Exception lastException = null;

        for (int attempt = 1; attempt <= 3; attempt++) {
            try {
                return executor.execute();
            } catch (Exception e) {
                lastException = e;
                LOGGER.warn("{} failed (attempt {}/3): {}", operation, attempt, e.getMessage());

                if (attempt < 3) {
                    try {
                        Thread.sleep(1000 * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new Exception("Operation interrupted", ie);
                    }
                }
            }
        }

        throw new Exception(operation + " failed after 3 attempts", lastException);
    }

    @FunctionalInterface
    private interface HttpRequestExecutor<T> {
        T execute() throws Exception;
    }

    /**
     * 关闭客户端
     */
    public void shutdown() {
        connected = false;
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
    }
    
    /**
     * 测试连接并检查服务器重启
     */
    private void testConnectionAndCheckRestart() throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/health")
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Health check failed: " + response.code());
            }

            // 解析响应获取服务器启动时间
            String responseBody = response.body().string();
            Map<String, Object> healthData = objectMapper.readValue(responseBody, Map.class);

            Object startTimeObj = healthData.get("startTime");
            long newStartTime = 0;
            if (startTimeObj instanceof Number) {
                newStartTime = ((Number) startTimeObj).longValue();
            }

            // 检查服务器是否重启了
            if (serverStartTime > 0 && newStartTime > serverStartTime) {
                LOGGER.warn("Voice server restart detected! Previous start time: {}, new start time: {}",
                           serverStartTime, newStartTime);
                // 标记需要重新同步
                onServerRestart();
            }

            serverStartTime = newStartTime;
            LOGGER.info("Voice server health check passed, start time: {}", serverStartTime);
        }
    }

    /**
     * 处理服务器重启
     */
    private void onServerRestart() {
        LOGGER.info("Handling voice server restart - will resync all players");
        // 这个方法将在PlayerEventListener中被调用来重新同步所有玩家
    }
    
    /**
     * 玩家登录
     */
    public void playerLogin(PlayerInfo playerInfo) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("uuid", playerInfo.getUuid().toString());
        requestBody.put("name", playerInfo.getName());
        requestBody.put("serverName", config.getServerName());
        requestBody.put("permissions", playerInfo.getPermissions());
        
        if (playerInfo.getPosition() != null) {
            Position pos = playerInfo.getPosition();
            Map<String, Object> positionData = new HashMap<>();
            positionData.put("worldId", pos.getWorldId());
            positionData.put("x", pos.getX());
            positionData.put("y", pos.getY());
            positionData.put("z", pos.getZ());
            requestBody.put("position", positionData);
        }
        
        String json = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(json, JSON);
        
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/login")
                .header("Authorization", authHeader)
                .post(body)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Player login failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Player {} logged in successfully", playerInfo.getName());
        }
    }
    
    /**
     * 玩家登出
     */
    public void playerLogout(UUID playerUuid) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("uuid", playerUuid.toString());
        
        String json = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(json, JSON);
        
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/logout")
                .header("Authorization", authHeader)
                .post(body)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Player logout failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Player {} logged out successfully", playerUuid);
        }
    }
    
    /**
     * 更新玩家位置
     */
    public void updatePlayerPosition(UUID playerUuid, Position position) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("worldId", position.getWorldId());
        requestBody.put("x", position.getX());
        requestBody.put("y", position.getY());
        requestBody.put("z", position.getZ());
        
        String json = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(json, JSON);
        
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/" + playerUuid + "/position")
                .header("Authorization", authHeader)
                .put(body)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Position update failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Position updated for player {}", playerUuid);
        }
    }
    
    /**
     * 更新玩家权限
     */
    public void updatePlayerPermissions(UUID playerUuid, List<String> permissions) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("permissions", permissions);
        
        String json = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(json, JSON);
        
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/" + playerUuid + "/permissions")
                .header("Authorization", authHeader)
                .put(body)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Permissions update failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Permissions updated for player {}", playerUuid);
        }
    }
    
    /**
     * 确保玩家已在语音服务器中注册
     */
    public void ensurePlayerRegistered(Player player) throws Exception {
        // 构建玩家登录请求
        Map<String, Object> loginData = Map.of(
            "uuid", player.getUniqueId().toString(),
            "name", player.getName(),
            "serverName", config.getServerName()
        );

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(loginData), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/login")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Player registration failed for {}: {} {}", player.getName(), response.code(), responseBody);
                // 不抛出异常，允许继续尝试生成密钥
            } else {
                LOGGER.debug("Player {} registered successfully", player.getName());
            }
        }
    }

    /**
     * 生成玩家认证密钥
     */
    public String generatePlayerSecret(UUID playerUuid) throws Exception {
        return executeWithRetry("Generate secret for " + playerUuid, () -> {
            Request request = new Request.Builder()
                    .url(baseUrl + "/api/players/" + playerUuid + "/secret")
                    .header("Authorization", authHeader)
                    .post(RequestBody.create("", JSON))
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Secret generation failed: " + response.code() + " " + response.body().string());
                }

                String responseBody = response.body().string();
                Map<String, Object> result = objectMapper.readValue(responseBody, Map.class);
                String secret = (String) result.get("secret");

                LOGGER.debug("Generated secret for player {}", playerUuid);
                return secret;
            }
        });
    }
    
    /**
     * 创建群组
     * @return 创建成功时返回群组UUID，失败时返回null
     */
    public UUID createGroup(UUID playerUuid, String groupName, String password, int groupType) throws Exception {
        Map<String, Object> groupData = new HashMap<>();
        groupData.put("name", groupName);
        groupData.put("ownerUuid", playerUuid.toString()); // 修复字段名：creatorUuid -> ownerUuid
        groupData.put("type", groupType);
        if (password != null) {
            groupData.put("password", password);
        }

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(groupData), JSON);
        Request request = new Request.Builder()
                .url(config.getVoiceServer().getApiEndpoint() + "/api/groups")
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();

                // 解析响应获取群组UUID
                Map<String, Object> responseData = objectMapper.readValue(responseBody, Map.class);

                // 从嵌套的group对象中获取id
                Map<String, Object> groupInfo = (Map<String, Object>) responseData.get("group");
                String groupUuidStr = groupInfo != null ? (String) groupInfo.get("id") : null;

                if (groupUuidStr != null) {
                    UUID groupUuid = UUID.fromString(groupUuidStr);
                    LOGGER.debug("Successfully created group {} with UUID {} for player {}", groupName, groupUuid, playerUuid);
                    return groupUuid;
                } else {
                    LOGGER.warn("Group created but no UUID returned in response");
                    return null;
                }
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to create group {}: {} {}", groupName, response.code(), responseBody);
                return null;
            }
        }
    }

    /**
     * 加入群组
     */
    public boolean joinGroup(UUID playerUuid, UUID groupUuid, String password) throws Exception {
        Map<String, Object> joinData = new HashMap<>();
        joinData.put("playerUuid", playerUuid.toString());
        if (password != null) {
            joinData.put("password", password);
        }

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(joinData), JSON);
        Request request = new Request.Builder()
                .url(config.getVoiceServer().getApiEndpoint() + "/api/groups/" + groupUuid + "/members")
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.debug("Successfully joined group {} for player {}", groupUuid, playerUuid);
                return true;
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to join group {}: {} {}", groupUuid, response.code(), responseBody);
                return false;
            }
        }
    }

    /**
     * 离开群组
     */
    public boolean leaveGroup(UUID playerUuid) throws Exception {
        String url = config.getVoiceServer().getApiEndpoint() + "/api/players/" + playerUuid + "/group";

        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .delete()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Successfully left group for player {}", playerUuid);
                return true;
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to leave group for player {}: {} - {}", playerUuid, response.code(), responseBody);
                return false;
            }
        }
    }

    /**
     * 获取服务器状态
     */
    public Map<String, Object> getServerStatus() throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/status")
                .header("Authorization", authHeader)
                .get()
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Status request failed: " + response.code() + " " + response.body().string());
            }
            
            String responseBody = response.body().string();
            return objectMapper.readValue(responseBody, Map.class);
        }
    }
}
