package de.maxhenkel.voicechat.adapter.managers;

import de.maxhenkel.voicechat.adapter.VoiceChatAdapterPlugin;
import de.maxhenkel.voicechat.adapter.model.PlayerState;
import de.maxhenkel.voicechat.adapter.network.PlayerStateNetworkManager;
import de.maxhenkel.voicechat.adapter.network.VoiceServerClient;
import de.maxhenkel.voicechat.adapter.util.FriendlyByteBuf;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Collection;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 玩家状态管理器 - 管理所有玩家的语音聊天状态
 */
public class PlayerStateManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerStateManager.class);
    
    private final VoiceChatAdapterPlugin plugin;
    private final ConcurrentHashMap<UUID, PlayerState> states;
    private final PlayerStateNetworkManager networkManager;
    private BukkitRunnable connectionSyncTask;

    public PlayerStateManager(VoiceChatAdapterPlugin plugin) {
        this.plugin = plugin;
        this.states = new ConcurrentHashMap<>();
        this.networkManager = new PlayerStateNetworkManager(plugin);
    }

    /**
     * 初始化网络管理器
     */
    public void initialize() {
        networkManager.register();
        startConnectionSyncTask();
        LOGGER.info("PlayerStateManager initialized with network support");
    }

    /**
     * 关闭网络管理器
     */
    public void shutdown() {
        stopConnectionSyncTask();
        networkManager.unregister();
        LOGGER.info("PlayerStateManager shutdown");
    }

    /**
     * 启动连接状态同步任务
     */
    private void startConnectionSyncTask() {
        connectionSyncTask = new BukkitRunnable() {
            @Override
            public void run() {
                syncConnectionStates();
            }
        };
        // 每5秒同步一次连接状态
        connectionSyncTask.runTaskTimerAsynchronously(plugin, 100L, 100L); // 5秒后开始，每5秒执行一次
    }

    /**
     * 停止连接状态同步任务
     */
    private void stopConnectionSyncTask() {
        if (connectionSyncTask != null) {
            connectionSyncTask.cancel();
            connectionSyncTask = null;
        }
    }

    /**
     * 同步连接状态
     */
    private void syncConnectionStates() {
        try {
            VoiceServerClient voiceServerClient = plugin.getVoiceServerClient();
            if (voiceServerClient == null || !voiceServerClient.isConnected()) {
                return;
            }

            Map<UUID, Boolean> connectionStates = voiceServerClient.getPlayerConnectionStates();
            boolean hasChanges = false;

            for (Map.Entry<UUID, Boolean> entry : connectionStates.entrySet()) {
                UUID playerUuid = entry.getKey();
                boolean isConnected = entry.getValue();

                PlayerState state = states.get(playerUuid);
                if (state != null) {
                    boolean wasDisconnected = state.isDisconnected();
                    boolean shouldBeDisconnected = !isConnected;

                    if (wasDisconnected != shouldBeDisconnected) {
                        state.setDisconnected(shouldBeDisconnected);
                        broadcastState(state);
                        hasChanges = true;
                    }
                }
            }



        } catch (Exception e) {
            LOGGER.warn("Failed to sync connection states: {}", e.getMessage());
        }
    }
    
    /**
     * 玩家加入服务器时的默认状态
     */
    public void onPlayerJoin(Player player) {
        PlayerState state = createDefaultState(player);
        states.put(player.getUniqueId(), state);
        broadcastState(state);
        LOGGER.debug("Setting default state for {}: {}", player.getName(), state);
    }
    
    /**
     * 玩家离开服务器
     */
    public void onPlayerQuit(Player player) {
        PlayerState state = new PlayerState(player.getUniqueId(), player.getName(), false, true, null);
        states.remove(player.getUniqueId());
        broadcastState(state);
        LOGGER.debug("Removing state for {}", player.getName());
    }
    
    /**
     * 玩家连接到语音聊天
     */
    public void onPlayerVoicechatConnect(Player player) {
        PlayerState state = states.get(player.getUniqueId());
        if (state == null) {
            state = createDefaultState(player);
        }
        
        state.setDisconnected(false);
        states.put(player.getUniqueId(), state);
        broadcastState(state);
        LOGGER.debug("Set {} to voice connected: {}", player.getName(), state);
    }
    
    /**
     * 玩家断开语音聊天连接
     */
    public void onPlayerVoicechatDisconnect(UUID playerUuid) {
        PlayerState state = states.get(playerUuid);
        if (state == null) {
            return;
        }
        
        state.setDisconnected(true);
        broadcastState(state);
        LOGGER.debug("Set {} to voice disconnected: {}", playerUuid, state);
    }
    
    /**
     * 设置玩家的群组
     */
    public void setGroup(Player player, UUID groupId) {
        PlayerState state = states.get(player.getUniqueId());
        if (state == null) {
            state = createDefaultState(player);
            LOGGER.debug("Creating default state for {}: {}", player.getName(), state);
        }
        
        state.setGroup(groupId);
        states.put(player.getUniqueId(), state);
        broadcastState(state);
        LOGGER.info("Setting group for {}: groupId={}", player.getName(), groupId);
    }
    
    /**
     * 更新玩家状态（禁用/启用语音）
     */
    public void updatePlayerState(Player player, boolean disabled) {
        PlayerState state = states.get(player.getUniqueId());
        if (state == null) {
            state = createDefaultState(player);
        }
        
        state.setDisabled(disabled);
        states.put(player.getUniqueId(), state);
        broadcastState(state);
        LOGGER.debug("Updated state for {}: disabled={}", player.getName(), disabled);
    }
    
    /**
     * 获取玩家状态
     */
    public PlayerState getState(UUID playerUuid) {
        return states.get(playerUuid);
    }
    
    /**
     * 获取所有玩家状态
     */
    public Collection<PlayerState> getStates() {
        return states.values();
    }
    
    /**
     * 向新加入的玩家发送所有玩家状态
     */
    public void sendAllStatesToPlayer(Player player) {
        try {
            // 检查是否有状态需要发送
            if (states.isEmpty()) {
                LOGGER.debug("No player states to send to {}", player.getName());
                return;
            }

            // 使用网络管理器发送状态
            networkManager.sendAllPlayerStates(player, states);
            LOGGER.debug("Sent {} player states to {}", states.size(), player.getName());

        } catch (Exception e) {
            LOGGER.error("Failed to send player states to {}: {}", player.getName(), e.getMessage());
        }
    }
    
    /**
     * 广播玩家状态更新给所有在线玩家
     */
    private void broadcastState(PlayerState state) {
        try {
            // 使用网络管理器广播状态
            networkManager.broadcastPlayerState(state);
            LOGGER.debug("Broadcasted state update: {}", state);
        } catch (Exception e) {
            LOGGER.error("Failed to broadcast player state: {}", e.getMessage());
        }
    }
    
    /**
     * 创建默认玩家状态
     */
    private PlayerState createDefaultState(Player player) {
        return new PlayerState(player.getUniqueId(), player.getName(), false, true);
    }
}
